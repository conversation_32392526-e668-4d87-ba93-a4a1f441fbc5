#!/usr/bin/env python3
"""
Script to process multiple stories from a markdown file and generate automated content.
Each story is defined by H1 headings (#) and will be processed through DeepSeek and Google TTS.
The script creates directories named after the headings and generates:
- for-deepseek.md (full prompt sent to DeepSeek)
- for-tts.md (DeepSeek result)
- {directory_name}.wav (audio file)
"""

import os
import re
import subprocess
import argparse
import sys
from pathlib import Path


def estimate_tokens(text: str) -> int:
    """Approximate token estimation, assuming 4 characters per token."""
    return len(text) // 4


def estimate_tokens_format_nicely(text: str) -> str:
    """Format token count nicely with comma separators."""
    estimated_tokens = estimate_tokens(text)
    return f"{estimated_tokens:,}"


def read_file_content(filename: str) -> str:
    """Read content of a file, return empty string if file doesn't exist."""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return f.read().strip()
    except FileNotFoundError:
        print(f"Warning: File '{filename}' not found, skipping...")
        return ""


def get_tts_style(story_dir: str, default_style: str = "Lies spannend vor") -> str:
    """Get TTS style from tts-style.md file or create it with default style."""
    style_file = os.path.join(story_dir, "tts-style.md")
    
    if os.path.exists(style_file):
        # Read existing style file
        try:
            with open(style_file, 'r', encoding='utf-8') as f:
                style = f.read().strip()
                if style:
                    return style
        except Exception as e:
            print(f"Warning: Could not read {style_file}: {e}")
    
    # Create style file with default style
    try:
        with open(style_file, 'w', encoding='utf-8') as f:
            f.write(f"{default_style}\n")
        # print(f"📝 Created tts-style.md with default style: {default_style}")
    except Exception as e:
        print(f"Warning: Could not create {style_file}: {e}")
    
    return default_style


def fill_placeholders(prompt_content: str) -> str:
    """Fill in file placeholders with actual content.

    Placeholders are defined as any text enclosed in {curly braces} and are
    treated as file paths. If any referenced file does not exist, print an
    error and exit with a non-zero status code.
    """
    # Find all placeholders of the form {anything}
    pattern = r'\{([^}]+)\}'
    matches = list(re.finditer(pattern, prompt_content))

    if not matches:
        return prompt_content

    # Collect placeholders and check existence first
    placeholders = [m.group(1).strip() for m in matches]
    missing_files = [p for p in placeholders if not os.path.exists(p)]

    if missing_files:
        print("Error: Missing placeholder files referenced in base prompt:")
        for p in missing_files:
            print(f"  - {p}")
        sys.exit(1)

    # All files exist -> perform replacements
    result = prompt_content
    for match in matches:
        filename = match.group(1).strip()
        content = read_file_content(filename)
        formatted_content = f"{content}\n"
        result = result.replace(match.group(0), formatted_content)

    return result


def generate_prompt_file(story_content: str, output_file: str) -> bool:
    """Generate the full prompt file by combining template with story content."""
    prompt_template_file = "snail-man-base-prompt.md"
    
    if not os.path.exists(prompt_template_file):
        print(f"Error: '{prompt_template_file}' not found!")
        return False
    
    # Read the prompt template and keep in memory (don't write to template file)
    with open(prompt_template_file, 'r', encoding='utf-8') as f:
        prompt_content = f.read()
    
    full_prompt = fill_placeholders(prompt_content)
    full_prompt += f"{story_content}"
    
    # Only write to the output file (for-deepseek.md)
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(full_prompt)
    
    return True


def sanitize_filename(text: str) -> str:
    """Clean text to create a valid filename."""
    # Remove markdown heading markers
    text = re.sub(r'^#+\s*', '', text.strip())
    
    # Remove markdown bold/italic markers
    text = text.strip('*').strip('_').strip()
    
    # Remove invalid filename characters
    text = re.sub(r'[<>:"/\\|?*]', '', text)
    
    # Replace multiple spaces with single space
    text = re.sub(r'\s+', ' ', text).strip()
    
    # Limit length to reasonable filename size
    if len(text) > 80:
        text = text[:77] + "..."
    
    return text if text else "untitled"


def extract_title_and_create_filename(content: str) -> str:
    """Extract title from first line and create a clean filename."""
    lines = content.strip().split('\n')
    if not lines:
        return "untitled"
    
    first_line = lines[0].strip()
    
    # Remove markdown bold markers
    first_line = first_line.strip('*').strip()
    
    # Remove common prefixes to keep filename short
    prefixes_to_remove = ['Snail Man vs. ', 'Snail Man ']
    for prefix in prefixes_to_remove:
        if first_line.startswith(prefix):
            first_line = first_line[len(prefix):]
            break
    
    # Take first 50 characters and add ellipsis if truncated
    if len(first_line) > 50:
        first_line = first_line[:47] + "..."
    
    # Clean filename - remove invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '', first_line)
    filename = re.sub(r'\s+', ' ', filename).strip()
    
    return filename


def make_unique_dirname(base_path: str, dirname: str) -> str:
    """Make directory name unique by appending index if it already exists."""
    full_path = os.path.join(base_path, dirname)
    if not os.path.exists(full_path):
        return dirname
    
    counter = 2
    while True:
        new_dirname = f"{dirname} ({counter})"
        new_full_path = os.path.join(base_path, new_dirname)
        if not os.path.exists(new_full_path):
            return new_dirname
        counter += 1


def make_unique_filename(base_path: str, filename: str) -> str:
    """Make filename unique by appending index if it already exists."""
    full_path = os.path.join(base_path, filename)
    if not os.path.exists(full_path):
        return filename
    
    counter = 2
    name, ext = os.path.splitext(filename)
    while True:
        new_filename = f"{name} ({counter}){ext}"
        new_full_path = os.path.join(base_path, new_filename)
        if not os.path.exists(new_full_path):
            return new_filename
        counter += 1


def parse_markdown_stories(content: str) -> list:
    """Parse markdown content and extract stories based on H1 headings."""
    stories = []
    lines = content.split('\n')
    current_story = []
    current_title = None
    
    for line in lines:
        # Check if line is an H1 heading
        if line.strip().startswith('# ') and len(line.strip()) > 2:
            # Save previous story if exists
            if current_title and current_story:
                story_content = '\n'.join(current_story).strip()
                if story_content:
                    stories.append({
                        'title': current_title,
                        'content': story_content
                    })
            
            # Start new story
            current_title = line.strip()
            current_story = []
        else:
            # Add line to current story content
            if current_title:  # Only add if we have a title
                current_story.append(line)
    
    # Don't forget the last story
    if current_title and current_story:
        story_content = '\n'.join(current_story).strip()
        if story_content:
            stories.append({
                'title': current_title,
                'content': story_content
            })
    
    return stories


def run_deepseek(prompt_file: str, output_file: str) -> bool:
    """Run DeepSeek wrapper with the prompt file."""
    deepseek_path = "/Users/<USER>/Developer/deepseek-website-wrapper/deepseek_wrapper.py"
    
    # Use the correct Python path from the user's environment
    python_path = "/Users/<USER>/.python-venv-all/bin/python3"
    
    cmd = [
        python_path, deepseek_path,
        "--prompt-file", prompt_file,
        "--output-file", output_file,
        "--no-wait-on-enter-before-quit"
    ]
    
    try:
        print(f"Running: {' '.join(cmd)}\n")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(f"✓ DeepSeek completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running DeepSeek: {e}")
        print(f"Return code: {e.returncode}")
        print(f"Stdout: {e.stdout}")
        print(f"Stderr: {e.stderr}")
        
        # Check if it's a missing dependency issue
        if "ModuleNotFoundError" in e.stderr:
            print("💡 Suggestion: Install missing Python dependencies in the virtual environment")
            print(f"Try: {python_path} -m pip install pyperclip")
        
        return False
    except FileNotFoundError as e:
        print(f"Error: Command not found: {e}")
        print(f"DeepSeek wrapper path: {deepseek_path}")
        print(f"Python path: {python_path}")
        return False


def run_google_tts(input_file: str, output_file: str, style: str = None) -> bool:
    """Run Google Gemini TTS with the input file, enforcing a 30-minute timeout."""
    cmd = [
        "google-gemini-tts",
        "--prompt-file", input_file,
        "--output-file", output_file
    ]

    # Add style parameter if provided
    if style:
        cmd.extend(["--style", style])

    try:
        print(f"Running: {' '.join(cmd)}")
        # Enforce a 30-minute timeout (1800 seconds)
        result = subprocess.run(cmd, capture_output=True, text=True, check=True, timeout=1800)
        print(f"✓ Google TTS completed successfully")
        return True
    except subprocess.TimeoutExpired as e:
        print("❌ Error: TTS generation exceeded 30 minutes and was terminated.")
        # e.stdout/e.stderr may be bytes or strings depending on Python; ensure safe printing
        try:
            stdout = e.stdout if isinstance(e.stdout, str) else (e.stdout.decode('utf-8', errors='ignore') if e.stdout else '')
            stderr = e.stderr if isinstance(e.stderr, str) else (e.stderr.decode('utf-8', errors='ignore') if e.stderr else '')
        except Exception:
            stdout, stderr = '', ''
        if stdout:
            print(f"Stdout (partial): {stdout}")
        if stderr:
            print(f"Stderr (partial): {stderr}")
        return False
    except subprocess.CalledProcessError as e:
        print(f"Error running Google TTS: {e}")
        print(f"Stdout: {e.stdout}")
        print(f"Stderr: {e.stderr}")
        return False
    except FileNotFoundError:
        print(f"Error: google-gemini-tts command not found")
        return False


def process_single_story(story_title: str, story_content: str, story_index: int, automated_dir: str, style: str = "Lies spannend vor") -> bool:
    """Process a single story through the complete pipeline."""
    print(f"\n--- Processing Story {story_index + 1}: {story_title} ---")
    
    # Generate directory name from story title
    dir_name = sanitize_filename(story_title)
    if not dir_name or dir_name == "untitled":
        dir_name = f"story_{story_index + 1}"
    
    # Make directory name unique
    dir_name = make_unique_dirname(automated_dir, dir_name)
    story_dir = os.path.join(automated_dir, dir_name)
    
    # Create story directory
    os.makedirs(story_dir, exist_ok=True)
    print(f"📁 Created directory: {story_dir}")
    
    # Generate full prompt content (combining template with story content)
    input_file = os.path.join(story_dir, "for-deepseek.md")
    if not generate_prompt_file(story_content, input_file):
        return False
    
    # Read the generated prompt for token estimation
    with open(input_file, 'r', encoding='utf-8') as f:
        full_prompt = f.read()
    
    token_estimate = estimate_tokens_format_nicely(full_prompt)
    print(f"📝 Generated full prompt ({token_estimate} tokens)")
    print(f"💾 Saved for-deepseek.md")
    
    # Run DeepSeek
    output_file = os.path.join(story_dir, "for-tts.md")
    if not run_deepseek(input_file, output_file):
        print(f"❌ Failed to run DeepSeek for story {story_index + 1}")
        return False
    
    # Read DeepSeek output for token estimation
    with open(output_file, 'r', encoding='utf-8') as f:
        output_content = f.read().strip()
    
    final_story_token_estimate = estimate_tokens_format_nicely(output_content)
    print(f"📖 Generated for-tts.md ({final_story_token_estimate} tokens)")
    
    # Get or create TTS style for this story
    tts_style = get_tts_style(story_dir, style)
    
    # Run Google TTS using for-tts.md, audio file named after directory
    audio_filename = f"{dir_name}.wav"
    audio_file = os.path.join(story_dir, audio_filename)
    import time
    start_time = time.time()
    print(f"\n🎤 Starting TTS generation (this may take a while)...")
    print(f"   Input: for-tts.md")
    print(f"   Output: {audio_filename}")
    print(f"   Style: {tts_style}")
    if not run_google_tts(output_file, audio_file, tts_style):
        print(f"❌ Failed to run Google TTS for story {story_index + 1}")
        return False
    elapsed = time.time() - start_time
    mins = int(elapsed // 60)
    secs = int(elapsed % 60)
    print(f"✅ Successfully processed story {story_index + 1} ({mins} minutes {secs} seconds)")
    return True


def debug_base_prompt():
    """Debug function to fill placeholders in base prompt, print to stdout and copy to clipboard."""
    prompt_template_file = "snail-man-base-prompt.md"
    
    if not os.path.exists(prompt_template_file):
        print(f"Error: '{prompt_template_file}' not found!")
        sys.exit(1)
    
    # Read the prompt template
    with open(prompt_template_file, 'r', encoding='utf-8') as f:
        prompt_content = f.read()
    
    # Check for placeholders and verify all files exist before filling
    pattern = r'\{([^}]+\.md)\}'
    matches = re.finditer(pattern, prompt_content)
    missing_files = []
    
    for match in matches:
        filename = match.group(1)
        if not os.path.exists(filename):
            missing_files.append(filename)
    
    if missing_files:
        print(f"Error: Missing placeholder files:")
        for filename in missing_files:
            print(f"  - {filename}")
        sys.exit(1)
    
    # Fill placeholders (all files exist now)
    filled_prompt = fill_placeholders(prompt_content)
    
    # Print to stdout
    print(filled_prompt)
    
    # Copy to clipboard
    try:
        import pyperclip
        pyperclip.copy(filled_prompt)
        print("✅ Copied to clipboard")
    except ImportError:
        print("⚠️  pyperclip not available - could not copy to clipboard")
    except Exception as e:
        print(f"⚠️  Could not copy to clipboard: {e}")


def main():
    """Main function to process stories from input file or resume processing from automated/ directories."""
    parser = argparse.ArgumentParser(description='Process stories through DeepSeek and Google TTS')
    parser.add_argument('input_file', nargs='?', help='Input markdown file containing stories with H1 headings (optional - if not provided, will resume processing from automated/ directories)')
    parser.add_argument('--style', default='Lies spannend vor', help='TTS style to use (default: "Lies spannend vor")')
    parser.add_argument('--debug-base-prompt', action='store_true', help='Fill placeholders in base prompt, print to stdout, copy to clipboard and exit')
    args = parser.parse_args()
    
    if args.debug_base_prompt:
        debug_base_prompt()
        sys.exit(0)
    
    if args.input_file:
        if not os.path.exists(args.input_file):
            print(f"Error: Input file '{args.input_file}' not found!")
            sys.exit(1)
        process_from_markdown_file(args.input_file, args.style)
    else:
        process_from_automated_directories(args.style)


def process_from_markdown_file(input_file: str, style: str = "Lies spannend vor"):
    """Process stories from a markdown file with H1 headings."""
    # Read input file
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Parse stories using H1 headings
    stories = parse_markdown_stories(content)
    
    if not stories:
        print("No stories found in input file! Make sure your file contains H1 headings (# Title)")
        sys.exit(1)
    
    print(f"Found {len(stories)} stories to process:")
    for i, story in enumerate(stories):
        print(f"  {i + 1}. {story['title']}")
    
    # Show processing preview
    print(f"\n🔍 Processing Preview:")
    automated_dir = "automated"
    has_collisions = False
    
    for i, story in enumerate(stories):
        print(f"\n--- Story {i + 1}: {story['title']} ---")
        
        # Show directory name that would be created
        dir_name = sanitize_filename(story['title'])
        if not dir_name or dir_name == "untitled":
            dir_name = f"story_{i + 1}"
        
        # Check for potential collisions
        story_dir = os.path.join(automated_dir, dir_name)
        if os.path.exists(story_dir):
            unique_dir_name = make_unique_dirname(automated_dir, dir_name)
            print(f"⚠️  Directory '{dir_name}' already exists!")
            print(f"📁 Will create directory: {unique_dir_name}")
            has_collisions = True
        else:
            print(f"📁 Will create directory: {dir_name}")
        
        # Show content info
        content_length = len(story['content'])
        estimated_tokens = estimate_tokens_format_nicely(story['content'])
        print(f"📝 Story content: {content_length} characters ({estimated_tokens} tokens)")
        print(f"🎯 Will generate: for-deepseek.md, for-tts.md")
        
        # Show audio filename (named after directory)
        final_dir_name = unique_dir_name if os.path.exists(story_dir) else dir_name
        audio_filename = f"{final_dir_name}.wav"
        print(f"🎤 Will generate audio: {audio_filename}")
    
    # Show warnings and get confirmation for actual processing
    print(f"\n" + "="*60)
    if has_collisions:
        print(f"⚠️  WARNING: Some directories already exist and will be renamed with (2), (3), etc.")
    
    print(f"🚀 Ready to process {len(stories)} stories through DeepSeek and Google TTS.")
    print(f"🎤 TTS Style: {style}")
    
    try:
        confirmation = input(f"\n📋 Press ENTER to continue or Ctrl+C to cancel: ")
        print(f"🎬 Starting processing...")
    except KeyboardInterrupt:
        print(f"\n❌ Processing cancelled by user.")
        return
    
    # Create automated directory
    automated_dir = "automated"
    os.makedirs(automated_dir, exist_ok=True)
    
    # Process each story
    successful = 0
    failed = 0
    
    for i, story in enumerate(stories):
        try:
            if process_single_story(story['title'], story['content'], i, automated_dir, style):
                successful += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Unexpected error processing story {i + 1}: {e}")
            failed += 1
    
    print(f"\n🎉 Processing complete!")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📁 Results saved in: {automated_dir}/")


def process_from_automated_directories(style: str = "Lies spannend vor"):
    """Resume processing from existing directories in automated/."""
    automated_dir = "automated"
    
    if not os.path.exists(automated_dir):
        print(f"❌ No '{automated_dir}' directory found!")
        print(f"💡 Create some directories with input.md files or provide a markdown file as input.")
        return
    
    # Find all subdirectories
    subdirs = [d for d in os.listdir(automated_dir) 
               if os.path.isdir(os.path.join(automated_dir, d))]
    
    if not subdirs:
        print(f"❌ No subdirectories found in '{automated_dir}/'!")
        return
    
    # Analyze what needs to be done for each directory
    tasks = []
    for dirname in sorted(subdirs):
        dir_path = os.path.join(automated_dir, dirname)
        input_file = os.path.join(dir_path, "for-deepseek.md")
        output_file = os.path.join(dir_path, "for-tts.md")
        
        # Find any .wav file in the directory
        wav_files = [f for f in os.listdir(dir_path) if f.endswith('.wav')]
        has_wav = len(wav_files) > 0
        
        if has_wav:
            tasks.append({
                'dir': dirname,
                'action': 'skip',
                'reason': f'WAV file exists: {wav_files[0]}'
            })
        elif os.path.exists(output_file):
            tasks.append({
                'dir': dirname,
                'action': 'tts_only',
                'reason': 'for-tts.md exists, need TTS'
            })
        elif os.path.exists(input_file):
            tasks.append({
                'dir': dirname,
                'action': 'full_process',
                'reason': 'for-deepseek.md exists, need DeepSeek + TTS'
            })
        else:
            tasks.append({
                'dir': dirname,
                'action': 'skip',
                'reason': 'No for-deepseek.md or for-tts.md found'
            })
    
    # Show processing preview (only directories that need processing)
    tts_count = sum(1 for task in tasks if task['action'] == 'tts_only')
    full_count = sum(1 for task in tasks if task['action'] == 'full_process')
    
    if tts_count == 0 and full_count == 0:
        print(f"\n✅ Nothing to process! All directories are complete or missing required files.")
        return
    
    for task in tasks:
        if task['action'] == 'skip':
            continue
            
        action_icon = '🎤' if task['action'] == 'tts_only' else '🚀'
        action_text = 'needs TTS' if task['action'] == 'tts_only' else 'needs DeepSeek + TTS'
        
        # Get the actual style that will be used for this directory
        dir_path = os.path.join(automated_dir, task['dir'])
        actual_style = get_tts_style(dir_path, style)
        
        print(f"{action_icon} {task['dir']}: {action_text} (style: {actual_style})")
    
    print(f"\n{tts_count + full_count} directories need processing.")
    
    try:
        confirmation = input(f"\n📋 Press ENTER to continue or Ctrl+C to cancel: ")
        print(f"🎬 Starting processing...")
    except KeyboardInterrupt:
        print(f"\n❌ Processing cancelled by user.")
        return
    
    # Process directories
    successful = 0
    failed = 0
    
    for task in tasks:
        if task['action'] == 'skip':
            continue
            
        dirname = task['dir']
        dir_path = os.path.join(automated_dir, dirname)
        
        try:
            if task['action'] == 'tts_only':
                if process_tts_only(dirname, dir_path, style):
                    successful += 1
                else:
                    failed += 1
            elif task['action'] == 'full_process':
                if process_full_from_input(dirname, dir_path, style):
                    successful += 1
                else:
                    failed += 1
        except Exception as e:
            print(f"❌ Unexpected error processing {dirname}: {e}")
            failed += 1
    
    print(f"\n🎉 Processing complete!")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📁 Results in: {automated_dir}/")


def process_tts_only(dirname: str, dir_path: str, style: str = "Lies spannend vor") -> bool:
    """Process TTS only for a directory that already has for-tts.md."""
    print(f"\n--- Processing TTS for: {dirname} ---")
    
    output_file = os.path.join(dir_path, "for-tts.md")
    audio_filename = f"{dirname}.wav"
    audio_file = os.path.join(dir_path, audio_filename)
    
    # Get or create TTS style for this story
    tts_style = get_tts_style(dir_path, style)
    
    import time
    start_time = time.time()
    print(f"🎤 Starting TTS generation (this may take a while)...")
    print(f"   Input: for-tts.md")
    print(f"   Output: {audio_filename}")
    print(f"   Style: {tts_style}")
    
    if not run_google_tts(output_file, audio_file, tts_style):
        print(f"❌ Failed to run Google TTS for {dirname}")
        return False
    
    elapsed = time.time() - start_time
    mins = int(elapsed // 60)
    secs = int(elapsed % 60)
    print(f"✅ Successfully generated audio for {dirname} ({mins} minutes {secs} seconds)")
    return True


def process_full_from_input(dirname: str, dir_path: str, style: str = "Lies spannend vor") -> bool:
    """Process DeepSeek + TTS for a directory that has for-deepseek.md."""
    print(f"\n--- Processing DeepSeek + TTS for: {dirname} ---")
    
    input_file = os.path.join(dir_path, "for-deepseek.md")
    
    # Read the full prompt from for-deepseek.md (should already contain complete prompt)
    with open(input_file, 'r', encoding='utf-8') as f:
        full_prompt = f.read().strip()
    
    token_estimate = estimate_tokens_format_nicely(full_prompt)
    print(f"📝 Using existing prompt from for-deepseek.md ({token_estimate} tokens)")
    
    # Run DeepSeek
    output_file = os.path.join(dir_path, "for-tts.md")
    if not run_deepseek(input_file, output_file):
        print(f"❌ Failed to run DeepSeek for {dirname}")
        return False
    
    # Read DeepSeek output for token estimation
    with open(output_file, 'r', encoding='utf-8') as f:
        output_content = f.read().strip()
    
    final_story_token_estimate = estimate_tokens_format_nicely(output_content)
    print(f"📖 Generated for-tts.md ({final_story_token_estimate} tokens)")
    
    # Get or create TTS style for this story
    tts_style = get_tts_style(dir_path, style)
    
    # Run Google TTS using for-tts.md
    audio_filename = f"{dirname}.wav"
    audio_file = os.path.join(dir_path, audio_filename)
    import time
    start_time = time.time()
    print(f"\n🎤 Starting TTS generation (this may take a while)...")
    print(f"   Input: for-tts.md")
    print(f"   Output: {audio_filename}")
    print(f"   Style: {tts_style}")
    if not run_google_tts(output_file, audio_file, tts_style):
        print(f"❌ Failed to run Google TTS for {dirname}")
        return False
    
    elapsed = time.time() - start_time
    mins = int(elapsed // 60)
    secs = int(elapsed % 60)
    print(f"✅ Successfully processed {dirname} ({mins} minutes {secs} seconds)")
    return True


if __name__ == "__main__":
    main()
