# Use the DeepSeek v3 model

First, I used `OpenRouter: DeepSeek v3`, but the free version got broken at some time.

So, I used https://github.com/Dima-369/python-cli-deepseek-website-wrapper which calls https://www.deepseek.com internally.

# <PERSON> Skript

```bash
deepseek --prompt-file full_snailman_prompt.md
```

# Info

Gerne die Unterheadings drin lassen, nicht l<PERSON>, laut <PERSON>.
Die sind nur `**bold**` ohne `#`.

Niemals zwei leere Zeilen benutzen, weil die Pausen zu lange werden können.

Keine `---` benutzen.

# Text to Speech

https://aistudio.google.com/generate-speech

Stimme: Enceladus (Mann mit leicht rauer Stimme)
Gemini 2.5 Pro Preview TTS

## Style

Lies spannend vor
