use clap::{Arg, ArgAction, Command};
use std::path::PathBuf;

#[derive(Debug, <PERSON>lone)]
pub struct Args {
    pub input_file: Option<PathBuf>,
    pub output_dir: PathBuf,
    pub style: String,
    pub debug_base_prompt: bool,
    pub dry_run: bool,
}

impl Default for Args {
    fn default() -> Self {
        Self {
            input_file: None,
            output_dir: PathBuf::from("/Users/<USER>/Developer/noah-geschichte-snail-man-schnecken-mann/automated/"),
            style: "Lies spannend vor".to_string(),
            debug_base_prompt: false,
            dry_run: false,
        }
    }
}

pub fn parse_args() -> Args {
    let matches = Command::new("snail-man-story-generator")
        .version("0.1.0")
        .about("Process stories through DeepSeek and Google TTS")
        .arg(
            Arg::new("input_file")
                .help("Input markdown file containing stories with H1 headings (optional - if not provided, will resume processing from automated/ directories)")
                .value_name("FILE")
                .index(1)
        )
        .arg(
            Arg::new("output-dir")
                .long("output-dir")
                .help("Output directory for generated content")
                .value_name("DIR")
                .default_value("/Users/<USER>/Developer/noah-geschichte-snail-man-schnecken-mann/automated/")
        )
        .arg(
            Arg::new("style")
                .long("style")
                .help("TTS style to use")
                .value_name("STYLE")
                .default_value("Lies spannend vor")
        )
        .arg(
            Arg::new("debug-base-prompt")
                .long("debug-base-prompt")
                .help("Fill placeholders in base prompt, print to stdout, copy to clipboard and exit")
                .action(ArgAction::SetTrue)
        )
        .arg(
            Arg::new("dry-run")
                .long("dry-run")
                .help("Show what would be done without actually processing (no confirmation required)")
                .action(ArgAction::SetTrue)
        )
        .get_matches();

    Args {
        input_file: matches.get_one::<String>("input_file").map(PathBuf::from),
        output_dir: PathBuf::from(matches.get_one::<String>("output-dir").unwrap()),
        style: matches.get_one::<String>("style").unwrap().to_string(),
        debug_base_prompt: matches.get_flag("debug-base-prompt"),
        dry_run: matches.get_flag("dry-run"),
    }
}
