use anyhow::{Context, Result};
use regex::Regex;
use std::path::Path;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Story {
    pub title: String,
    pub content: String,
}

/// Parse markdown content and extract stories based on H1 headings
pub fn parse_markdown_stories(content: &str) -> Vec<Story> {
    let mut stories = Vec::new();
    let lines: Vec<&str> = content.lines().collect();
    let mut current_story: Vec<String> = Vec::new();
    let mut current_title: Option<String> = None;

    for line in lines {
        let trimmed = line.trim();
        
        // Check if line is an H1 heading
        if trimmed.starts_with("# ") && trimmed.len() > 2 {
            // Save previous story if exists
            if let Some(title) = &current_title {
                if !current_story.is_empty() {
                    let story_content = current_story.join("\n").trim().to_string();
                    if !story_content.is_empty() {
                        stories.push(Story {
                            title: title.clone(),
                            content: story_content,
                        });
                    }
                }
            }
            
            // Start new story
            current_title = Some(trimmed.to_string());
            current_story.clear();
        } else if current_title.is_some() {
            // Add line to current story content
            current_story.push(line.to_string());
        }
    }
    
    // Don't forget the last story
    if let Some(title) = current_title {
        if !current_story.is_empty() {
            let story_content = current_story.join("\n").trim().to_string();
            if !story_content.is_empty() {
                stories.push(Story {
                    title,
                    content: story_content,
                });
            }
        }
    }
    
    stories
}

/// Clean text to create a valid filename
pub fn sanitize_filename(text: &str) -> String {
    // Remove markdown heading markers
    let re_heading = Regex::new(r"^#+\s*").unwrap();
    let text = re_heading.replace(text.trim(), "");
    
    // Remove markdown bold/italic markers
    let text = text.trim_matches('*').trim_matches('_').trim();
    
    // Remove invalid filename characters
    let re_invalid = Regex::new(r#"[<>:"/\\|?*]"#).unwrap();
    let text = re_invalid.replace_all(text, "");
    
    // Replace multiple spaces with single space
    let re_spaces = Regex::new(r"\s+").unwrap();
    let text = re_spaces.replace_all(&text, " ").trim().to_string();
    
    // Limit length to reasonable filename size
    if text.len() > 80 {
        format!("{}...", &text[..77])
    } else if text.is_empty() {
        "untitled".to_string()
    } else {
        text
    }
}

/// Extract title from first line and create a clean filename
pub fn extract_title_and_create_filename(content: &str) -> String {
    let lines: Vec<&str> = content.lines().collect();
    if lines.is_empty() {
        return "untitled".to_string();
    }
    
    let first_line = lines[0].trim();
    
    // Remove markdown bold markers
    let first_line = first_line.trim_matches('*').trim();
    
    // Remove common prefixes to keep filename short
    let prefixes_to_remove = ["Snail Man vs. ", "Snail Man "];
    let mut first_line = first_line.to_string();
    for prefix in &prefixes_to_remove {
        if first_line.starts_with(prefix) {
            first_line = first_line[prefix.len()..].to_string();
            break;
        }
    }
    
    // Take first 50 characters and add ellipsis if truncated
    if first_line.len() > 50 {
        first_line = format!("{}...", &first_line[..47]);
    }
    
    // Clean filename - remove invalid characters
    let re_invalid = Regex::new(r#"[<>:"/\\|?*]"#).unwrap();
    let filename = re_invalid.replace_all(&first_line, "");
    let re_spaces = Regex::new(r"\s+").unwrap();
    let filename = re_spaces.replace_all(&filename, " ").trim().to_string();
    
    filename
}

/// Make directory name unique by appending index if it already exists
pub fn make_unique_dirname(base_path: &Path, dirname: &str) -> String {
    let full_path = base_path.join(dirname);
    if !full_path.exists() {
        return dirname.to_string();
    }
    
    let mut counter = 2;
    loop {
        let new_dirname = format!("{} ({})", dirname, counter);
        let new_full_path = base_path.join(&new_dirname);
        if !new_full_path.exists() {
            return new_dirname;
        }
        counter += 1;
    }
}

/// Make filename unique by appending index if it already exists
pub fn make_unique_filename(base_path: &Path, filename: &str) -> String {
    let full_path = base_path.join(filename);
    if !full_path.exists() {
        return filename.to_string();
    }
    
    let mut counter = 2;
    let path = Path::new(filename);
    let name = path.file_stem().unwrap_or_default().to_string_lossy();
    let ext = path.extension().map(|e| format!(".{}", e.to_string_lossy())).unwrap_or_default();
    
    loop {
        let new_filename = format!("{} ({}){}", name, counter, ext);
        let new_full_path = base_path.join(&new_filename);
        if !new_full_path.exists() {
            return new_filename;
        }
        counter += 1;
    }
}

/// Approximate token estimation, assuming 4 characters per token
pub fn estimate_tokens(text: &str) -> usize {
    text.len() / 4
}

/// Format token count nicely with comma separators
pub fn estimate_tokens_format_nicely(text: &str) -> String {
    let estimated_tokens = estimate_tokens(text);
    format!("{:,}", estimated_tokens)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_markdown_stories() {
        let content = r#"# First Story
This is the first story content.
More content here.

# Second Story
This is the second story.

# Third Story
Final story content."#;

        let stories = parse_markdown_stories(content);
        assert_eq!(stories.len(), 3);
        assert_eq!(stories[0].title, "# First Story");
        assert!(stories[0].content.contains("This is the first story content."));
    }

    #[test]
    fn test_sanitize_filename() {
        assert_eq!(sanitize_filename("# Test Story"), "Test Story");
        assert_eq!(sanitize_filename("**Bold Story**"), "Bold Story");
        assert_eq!(sanitize_filename("Story/with\\invalid:chars"), "Storywithvalidchars");
    }
}
